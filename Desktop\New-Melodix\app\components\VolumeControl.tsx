'use client';

import React, { useState } from 'react';

const VolumeControl: React.FC = () => {
  const [volume, setVolume] = useState(60);

  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = e.clientX - rect.left;
    const width = progressBar.offsetWidth;
    const percentage = (clickPosition / width) * 100;
    setVolume(percentage);
  };

  return (
    <div className="volume-control">
      <button className="control-btn">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          <path d="M9.741.85a.75.75 0 01.375.65v13a.75.75 0 01-1.125.65l-6.925-4a3.642 3.642 0 01-1.33-4.967 3.639 3.639 0 011.33-1.332l6.925-4a.75.75 0 01.75 0zm-6.924 5.3a2.139 2.139 0 000 3.7l5.8 3.35V2.8l-5.8 3.35zm8.683 4.29V5.56a2.75 2.75 0 010 4.88z"/>
          <path d="M11.5 13.614a5.752 5.752 0 000-11.228v1.55a4.252 4.252 0 010 8.127v1.55z"/>
        </svg>
      </button>
      <div className="progress" onClick={handleVolumeChange}>
        <div className="progress-fill" style={{ width: `${volume}%` }}></div>
      </div>
    </div>
  );
};

export default VolumeControl;