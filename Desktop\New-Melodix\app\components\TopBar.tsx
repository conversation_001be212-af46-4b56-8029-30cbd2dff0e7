'use client';

import React from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';

const TopBar: React.FC = () => {
  const { data: session, status } = useSession();

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/login' });
  };

  const getInitials = (firstName: string, lastName: string) => {
    if (!firstName) return '';
    return firstName.charAt(0).toUpperCase() + (lastName ? lastName.charAt(0).toUpperCase() : '');
  };

  if (status === 'loading') {
    return (
      <div className="top-bar">
        <div className="nav-buttons">
          <button className="nav-btn">
            <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
              <path d="M11.03.47a.75.75 0 010 1.06L4.56 8l6.47 6.47a.75.75 0 11-1.06 1.06L2.44 8 9.97.47a.75.75 0 011.06 0z"/>
            </svg>
          </button>
          <button className="nav-btn">
            <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
              <path d="M4.97.47a.75.75 0 000 1.06L11.44 8l-6.47 6.47a.75.75 0 101.06 1.06L13.56 8 6.03.47a.75.75 0 00-1.06 0z"/>
            </svg>
          </button>
        </div>
        <div className="user-profile">
          <div className="avatar">L</div>
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="top-bar">
      <div className="nav-buttons">
        <button className="nav-btn">
          <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
            <path d="M11.03.47a.75.75 0 010 1.06L4.56 8l6.47 6.47a.75.75 0 11-1.06 1.06L2.44 8 9.97.47a.75.75 0 011.06 0z"/>
          </svg>
        </button>
        <button className="nav-btn">
          <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
            <path d="M4.97.47a.75.75 0 000 1.06L11.44 8l-6.47 6.47a.75.75 0 101.06 1.06L13.56 8 6.03.47a.75.75 0 00-1.06 0z"/>
          </svg>
        </button>
      </div>
      
      {session ? (
        <div className="user-profile">
          <div className="avatar">
            {getInitials(session.user.firstName, session.user.lastName)}
          </div>
          <div className="user-info">
            <span className="user-name">{session.user.firstName}</span>
            <button
              onClick={handleSignOut}
              className="sign-out-btn text-xs text-gray-400 hover:text-white transition-colors"
            >
              Sign out
            </button>
          </div>
        </div>
      ) : (
        <div className="user-profile">
          <Link href="/auth/login">
            <button className="login-btn text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-full transition-colors">
              Login
            </button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default TopBar;