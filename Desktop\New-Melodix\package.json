{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --webpack", "build": "next build --webpack", "start": "next start", "lint": "eslint"}, "dependencies": {"@next-auth/mongodb-adapter": "^1.0.2", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "mongoose": "^8.19.2", "next": "16.0.0", "next-auth": "^4.24.7", "react": "19.2.0", "react-dom": "19.2.0", "zod": "^4.1.12"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "16.0.0", "tailwindcss": "^4", "typescript": "^5"}}