'use client';

import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import TopBar from './TopBar';
import QuickPlayCard from './QuickPlayCard';
import Card from './Card';
import Player from './Player';
import Providers from './layout/Providers';

const HomePage: React.FC = () => {
  const [greeting, setGreeting] = useState('Good evening');

  // Update greeting based on time
  useEffect(() => {
    const updateGreeting = () => {
      const hour = new Date().getHours();
      
      if (hour < 12) {
        setGreeting('Good morning');
      } else if (hour < 18) {
        setGreeting('Good afternoon');
      } else {
        setGreeting('Good evening');
      }
    };

    updateGreeting();
  }, []);

  const quickPlayData = [
    { title: 'Liked Songs', color: '#e91e63' },
    { title: 'Chill Vibes', color: '#9c27b0' },
    { title: 'Workout Mix', color: '#f44336' },
    { title: 'Focus Flow', color: '#009688' },
    { title: 'Party Hits', color: '#ff9800' },
    { title: 'Road Trip', color: '#3f51b5' },
  ];

  const madeForYouData = [
    { 
      title: 'Daily Mix 1', 
      description: 'The Weeknd, Drake, Travis Scott and more', 
      color: '#4caf50' 
    },
    { 
      title: 'Discover Weekly', 
      description: 'Your weekly mixtape of fresh music', 
      color: '#2196f3' 
    },
    { 
      title: 'Release Radar', 
      description: 'Catch all the latest music from artists you follow', 
      color: '#ff5722' 
    },
    { 
      title: 'Repeat Rewind', 
      description: 'Songs you can\'t stop playing', 
      color: '#673ab7' 
    },
    { 
      title: 'Summer Hits', 
      description: 'The hottest tracks for sunny days', 
      color: '#795548' 
    },
    { 
      title: 'Acoustic Favorites', 
      description: 'Stripped down versions of your favorites', 
      color: '#607d8b' 
    },
  ];

  const popularPlaylistsData = [
    { 
      title: 'Today\'s Top Hits', 
      description: 'Ed Sheeran is on top of the Hottest 50!', 
      color: '#e91e63' 
    },
    { 
      title: 'RapCaviar', 
      description: 'New music from Kendrick Lamar, Travis Scott and more', 
      color: '#009688' 
    },
    { 
      title: 'Rock Classics', 
      description: 'Rock legends & epic songs', 
      color: '#ff9800' 
    },
    { 
      title: 'Jazz Vibes', 
      description: 'The original chill instrumental music', 
      color: '#3f51b5' 
    },
    { 
      title: 'Mint', 
      description: 'The best in electronic music', 
      color: '#f44336' 
    },
    { 
      title: 'Pop Rising', 
      description: 'Rising pop stars and fresh favorites', 
      color: '#9c27b0' 
    },
  ];

  const handlePlayTrack = () => {
    // This would be handled by a music player context/state in a real app
    console.log('Playing track');
  };

  return (
    <Providers>
      <div className="container">
        <Sidebar activeItem="home" />
        
        <div className="main-content">
          <TopBar />
          
          <div className="content-section">
            <h1 className="greeting">{greeting}</h1>

            <div className="quick-play-grid">
              {quickPlayData.map((item, index) => (
                <QuickPlayCard
                  key={index}
                  title={item.title}
                  color={item.color}
                  onClick={handlePlayTrack}
                />
              ))}
            </div>

            <h2 className="section-title">Made for you</h2>
            <div className="cards-grid">
              {madeForYouData.map((item, index) => (
                <Card
                  key={index}
                  title={item.title}
                  description={item.description}
                  color={item.color}
                  onClick={handlePlayTrack}
                />
              ))}
            </div>

            <h2 className="section-title">Popular playlists</h2>
            <div className="cards-grid">
              {popularPlaylistsData.map((item, index) => (
                <Card
                  key={index}
                  title={item.title}
                  description={item.description}
                  color={item.color}
                  onClick={handlePlayTrack}
                />
              ))}
            </div>
          </div>
        </div>
        
        <Player />
      </div>
    </Providers>
  );
};

export default HomePage;