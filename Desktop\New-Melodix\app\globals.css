@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #000;
  color: #fff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100vw;
}

/* Custom Spotify-like styles */
.container {
  display: flex;
  height: 100vh;
  width: 100vw;
  max-width: 100vw;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background-color: #000;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  padding: 0 12px;
  margin-bottom: 10px;
  color: #1ed760;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  color: #b3b3b3;
  text-decoration: none;
  border-radius: 4px;
  transition: color 0.2s;
  cursor: pointer;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #fff;
}

.nav-item svg {
  width: 24px;
  height: 24px;
}

.divider {
  height: 1px;
  background-color: #282828;
  margin: 8px 0;
}

.playlist-item {
  padding: 8px 12px;
  color: #b3b3b3;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
}

.playlist-item:hover {
  color: #fff;
}

.main-content {
  flex: 1;
  background-color: #121212;
  overflow-y: auto;
  padding-bottom: 100px;
  width: 100%;
  min-width: 0;
}

.top-bar {
  background-color: #121212;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.nav-buttons {
  display: flex;
  gap: 16px;
}

.nav-btn {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.nav-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.user-profile {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 16px 4px 4px;
  border-radius: 23px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-profile:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.content-section {
  padding: 16px 32px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
}

.greeting {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
}

.quick-play-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.quick-play-card {
  background-color: #181818;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 16px;
  height: 80px;
  overflow: hidden;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.quick-play-card:hover {
  background-color: #282828;
}

.quick-play-card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}

.quick-play-card img {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.quick-play-card span {
  font-weight: 600;
  font-size: 16px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.card {
  background-color: #181818;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.card:hover {
  background-color: #282828;
}

.card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}

.card-image {
  width: 100%;
  aspect-ratio: 1;
  background-color: #333;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 48px;
  height: 48px;
  background-color: #1ed760;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transform: translateY(8px);
  transition: all 0.3s;
  box-shadow: 0 8px 8px rgba(0, 0, 0, 0.3);
}

.play-btn:hover {
  transform: scale(1.05) translateY(0);
  background-color: #1fdf64;
}

.play-btn svg {
  width: 20px;
  height: 20px;
  fill: #000;
  margin-left: 2px;
}

.card-title {
  font-weight: 600;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-description {
  font-size: 14px;
  color: #b3b3b3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #181818;
  border-top: 1px solid #282828;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.player-left {
  display: flex;
  align-items: center;
  gap: 14px;
  min-width: 180px;
  width: 30%;
}

.player-left img {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  background-color: #333;
}

.song-info h4 {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
}

.song-info p {
  font-size: 12px;
  color: #b3b3b3;
}

.heart-icon {
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
}

.heart-icon:hover {
  color: #fff;
}

.player-center {
  flex: 1;
  max-width: 722px;
}

.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
}

.control-btn {
  background: none;
  border: none;
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  display: flex;
  align-items: center;
}

.control-btn:hover {
  color: #fff;
}

.control-btn.play {
  background-color: #fff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn.play:hover {
  transform: scale(1.06);
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time {
  font-size: 12px;
  color: #b3b3b3;
  min-width: 40px;
}

.progress {
  flex: 1;
  height: 4px;
  background-color: #4d4d4d;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.progress:hover .progress-fill::after {
  opacity: 1;
}

.progress-fill {
  height: 100%;
  background-color: #fff;
  border-radius: 2px;
  width: 30%;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s;
}

.player-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  width: 30%;
  justify-content: flex-end;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 125px;
}

/* Authentication styles */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
}

.sign-out-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-top: 2px;
  text-align: left;
}

.login-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }

  .nav-item span, .playlist-item, .logo {
    display: none;
  }

  .logo::first-letter {
    display: block;
  }
}
