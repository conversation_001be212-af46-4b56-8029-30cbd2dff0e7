'use client';

import React from 'react';

interface CardProps {
  title: string;
  description: string;
  imageUrl?: string;
  color: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({ 
  title, 
  description, 
  imageUrl, 
  color, 
  onClick 
}) => {
  return (
    <div className="card" onClick={onClick}>
      <div className="card-image">
        <img 
          src={`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200'%3E%3Crect fill='${color}' width='200' height='200'/%3E%3C/svg%3E`} 
          alt={title}
        />
        <button className="play-btn">
          <svg viewBox="0 0 24 24">
            <path d="M8 5.14v14l11-7-11-7z"/>
          </svg>
        </button>
      </div>
      <div className="card-title">{title}</div>
      <div className="card-description">{description}</div>
    </div>
  );
};

export default Card;